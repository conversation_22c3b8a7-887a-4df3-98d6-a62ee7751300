# Optimized FastAPI HTTP Server Code

This document describes the optimizations applied to the provided FastAPI server code to improve readability, maintainability, and efficiency.

## Summary of Optimizations

1. **Session Retrieval Helper**  
   Created a helper function `get_current_session()` to centralize session retrieval and error handling, reducing repetitive code.

2. **Request JSON Extraction Helper**  
   Added a helper function `get_request_json(request)` to safely extract JSON from requests, improving error handling.

3. **Consolidated Similar Endpoints**  
   Grouped similar endpoints (e.g., code operations like debug, test, review, optimize) into a single handler using path parameters and a dispatch dictionary.

4. **Reduced Redundant Prints**  
   Removed duplicate or unnecessary print statements to clean logs.

5. **Consistent Error Responses**  
   Standardized error response format and status codes for missing parameters or sessions.

6. **Simplified Imports**  
   Removed unused imports and organized imports for clarity.

7. **Improved Path Parameter Access**  
   Used FastAPI path parameter declarations instead of accessing `request.path_params` directly.

8. **Added Type Hints**  
   Added type hints for better code clarity and editor support.

9. **General Code Cleanup**  
   Removed commented-out code and redundant comments.

---

## Optimized Code Snippet

```python
from fastapi import FastAPI, Request, HTTPException, Path, Query
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import json

from BASE.http.chat import (
    chat_stream,
    delete_chat_history,
    get_chat_history,
    get_all_chat_history,
)
from BASE.http.watchdog import watchdog_file_delete, watchdog_file_update
from BASE.http.kb.delete_kb import delete_kb_source
from BASE.http.kb.list_kbs import list_knowledge_bases
from BASE.http.auto_actions import auto_actions
from BASE.http.swagger import swagger_stream, swagger_list
from BASE.http.codelens import debug_code, optimize_code, review_code, test_code
from ipc import IPC
from startup_config import save_session, delete_session

ipc_ = IPC.connect()

app = FastAPI(title="CodeMate HTTP API")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def get_current_session() -> str:
    session = ipc_.get("current_session")
    if not session:
        raise HTTPException(status_code=401, detail="No session found")
    return session


async def get_request_json(request: Request) -> dict:
    try:
        return await request.json()
    except Exception:
        raise HTTPException(status_code=400, detail="Invalid JSON payload")


@app.get("/")
async def root():
    return {"message": "Hello World"}


@app.post("/chat/stream")
async def chat_stream_handler(request: Request):
    data = await get_request_json(request)
    session_id = get_current_session()
    conversation_id = data.get("conversation_id", "")

    return StreamingResponse(
        chat_stream(
            messages=data["messages"],
            call_for="chat",
            session_id=session_id,
            is_web_search=data.get("web_search", False),
            mode=data["mode"].upper(),
            conversation_id=conversation_id,
            provide_followups=data.get("provide_followups", False),
        ),
        media_type="text/event-stream",
    )


@app.post("/swagger/stream")
async def swagger_stream_handler(request: Request):
    data = await get_request_json(request)
    return StreamingResponse(
        swagger_stream(data),
        media_type="text/event-stream",
    )


@app.delete("/chat/history/{conversation_id}")
async def chat_history_delete_handler(conversation_id: str = Path(...)):
    result = await delete_chat_history(conversation_id)
    return JSONResponse(result)


@app.get("/chat/history/{conversation_id}")
async def chat_history_handler(conversation_id: str = Path(...)):
    result = await get_chat_history(conversation_id)
    return JSONResponse(result)


@app.post("/chat/history/all")
async def chat_history_all_handler():
    session_id = get_current_session()
    result = await get_all_chat_history(session_id)
    return JSONResponse(result)


@app.post("/swagger_list")
async def swagger_list_handler(request: Request):
    data = await get_request_json(request)
    result = await swagger_list(data)
    return JSONResponse(result)


@app.post("/delete_kb")
async def delete_kb_handler(request: Request):
    data = await get_request_json(request)
    kbid = data.get("kbid")
    if not kbid:
        raise HTTPException(status_code=400, detail="Knowledge base ID is required")
    result = await delete_kb_source(kbid=kbid)
    return JSONResponse(result)


@app.get("/list_kbs")
async def list_kb(include_cloud: bool = Query(False)):
    session = get_current_session()
    result = await list_knowledge_bases(include_cloud=include_cloud)
    return JSONResponse(result)


@app.post("/watchdog/file_delete")
async def watchdog_file_delete_handler(request: Request):
    data = await get_request_json(request)
    file_path = data.get("file_path")
    if not file_path:
        raise HTTPException(status_code=400, detail="File path not provided")
    result = await watchdog_file_delete(file_path=file_path)
    return JSONResponse(result)


@app.post("/watchdog/file_update")
async def watchdog_file_update_handler(request: Request):
    data = await get_request_json(request)
    file_path = data.get("file_path")
    if not file_path:
        raise HTTPException(status_code=400, detail="File path not provided")
    result = await watchdog_file_update(file_path=file_path)
    return JSONResponse(result)


# Consolidated code operation handlers
code_operations = {
    "debug": debug_code,
    "test": test_code,
    "review": review_code,
    "optimize": optimize_code,
}


@app.post("/auto-actions/{operation}")
async def auto_actions_handler(request: Request, operation: str = Path(...)):
    data = await get_request_json(request)
    session_id = get_current_session()
    result = await auto_actions(data, session_id=session_id, operation=operation)
    return JSONResponse(result)


@app.post("/code/{operation}")
async def code_operation_handler(request: Request, operation: str = Path(...)):
    operation_func = code_operations.get(operation)
    if not operation_func:
        raise HTTPException(status_code=404, detail="Operation not supported")
    data = await get_request_json(request)
    session_id = get_current_session()
    result = await operation_func(data, session_id=session_id)
    return JSONResponse(result)


@app.post("/register_meta")
async def register_meta(request: Request):
    data = await get_request_json(request)
    session_id = data.get("session_id", "")
    extension_version = data.get("extension_version", "")
    subsystem_version = data.get("subsystem_version", "")

    if not session_id:
        raise HTTPException(status_code=400, detail="No session ID provided")

    save_session(session_id)
    ipc_.set("current_session", session_id)
    ipc_.set("extension_version", extension_version)
    ipc_.set("subsystem_version", subsystem_version)

    return JSONResponse(
        {
            "session_id": session_id,
            "status": "success",
            "message": "Session ID registered successfully",
        }
    )


@app.get("/get_session")
async def get_session():
    current_session = ipc_.get("current_session")
    return JSONResponse(
        {
            "session_id": current_session,
            "status": "success",
            "message": "Session ID retrieved successfully",
        }
    )


@app.post("/logout")
async def logout(request: Request):
    data = await get_request_json(request)
    session_id = data.get("session_id", "")
    current_session = ipc_.get("current_session")

    if not session_id:
        raise HTTPException(status_code=400, detail="No session ID provided")

    if current_session == session_id:
        ipc_.set("current_session", None)
        delete_session()
        return JSONResponse(
            {
                "session_id": session_id,
                "status": "logged_out",
                "message": "Session cleared successfully",
            }
        )

    return JSONResponse(
        {
            "session_id": session_id,
            "status": "not_found",
            "message": "Session ID does not match current session",
        }
    )


if __name__ == "__main__":
    uvicorn.run("http_server:app", host="127.0.0.1", port=45213, workers=1)
```

---

## Notes

- The helper functions improve code reuse and error handling.
- Consolidating similar endpoints reduces code duplication and eases future maintenance.
- Using FastAPI's path and query parameter features improves clarity and validation.
- Raising `HTTPException` with appropriate status codes provides better API client feedback.
- The code is now cleaner, more maintainable, and easier to extend.