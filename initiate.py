import multiprocessing
import socket
import subprocess
import sys
import time
import requests

from ipc import IPC
from BASE.utils.platform_detector import PlatformDetector
from BASE.vdb.qdrant import get_qdrant_client, start_qdrant_server
from BASE.vdb.download import main as download_qdrant
import BASE.utils.check_cuda
from startup_config import perform_startup_configuration, read_session, session_refresh

def setup_multiprocessing_support():
    """Setup multiprocessing support for PyInstaller executables"""
    multiprocessing.freeze_support()
    
    # Additional fix for PyInstaller executables
    if hasattr(sys, 'frozen') and hasattr(sys, '_MEIPASS'):
        # We're running as a PyInstaller executable
        import os
        # Set multiprocessing start method to 'spawn' for better compatibility
        try:
            multiprocessing.set_start_method('spawn', force=True)
        except RuntimeError:
            # Start method already set, ignore
            pass

def is_port_open(host="localhost", port=11434, timeout=3):
    """Check if a port is open (service is running)."""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(timeout)
            result = sock.connect_ex((host, port))
            return result == 0
    except:
        return False

def check_ollama():
    """Check if Ollama is running."""
    running = is_port_open("localhost", 11434)
    ipc_.set("ollama_running", running)
    print(f"Ollama: {'Running' if running else 'Not running'}")

def check_internet():
    """Check internet connectivity."""
    try:
        response = requests.get("https://www.google.com", timeout=5)
        connected = response.status_code == 200
    except:
        connected = False
    
    ipc_.set("internet_connected", connected)
    print(f"Internet: {'Connected' if connected else 'Disconnected'}")

def setup_qdrant():
    """Setup Qdrant vector database."""
    # Download Qdrant
    print("Checking Qdrant download...")
    downloaded = download_qdrant()
    ipc_.set("qdrant_downloaded", downloaded)
    
    if not downloaded:
        print("Qdrant download failed")
        ipc_.set("qdrant_ready", False)
        return None
    
    # Check if already running
    if is_port_open("localhost", 45215):
        print("Qdrant already running")
        ipc_.set("qdrant_ready", True)
        return None
    
    # Start Qdrant server
    try:
        print("Starting Qdrant server...")
        qdrant_process = start_qdrant_server()
        
        # Wait for it to be ready
        for _ in range(30):  # 30 second timeout
            if is_port_open("localhost", 45215):
                ipc_.set("qdrant_ready", True)
                print("Qdrant server ready")
                return qdrant_process
            time.sleep(1)
        
        print("Qdrant server failed to start")
        ipc_.set("qdrant_ready", False)
        return qdrant_process
        
    except Exception as e:
        print(f"Qdrant setup failed: {e}")
        ipc_.set("qdrant_ready", False)
        return None

def get_python_executable():
    """Get the correct Python executable path for subprocess calls"""
    if hasattr(sys, 'frozen') and hasattr(sys, '_MEIPASS'):
        # We're running as a PyInstaller executable
        # Use the current executable for subprocess calls
        return sys.executable
    else:
        # We're running as a regular Python script
        return sys.executable

import os
import sys
import subprocess

import os
import sys
import subprocess

def start_servers():
    """Start HTTP and WebSocket servers and show logs in main terminal."""
    processes = []
    python_executable = sys.executable  # Full path to Python interpreter

    for server in ['http_server.py', 'websocket_server.py']:
        try:
            script_path = os.path.join(os.getcwd(), server)
            print(f"📂 Starting: {script_path}")

            process = subprocess.Popen(
                [python_executable, script_path],
                stdout=None,  # Show output directly
                stderr=None   # Show errors directly
            )

            processes.append(process)
            print(f"✅ Started {server} (PID: {process.pid})")

        except Exception as e:
            print(f"❌ Failed to start {server}: {e}")

    return processes

def startup_initialization():
    """Perform startup initialization for the session management system."""
    perform_startup_configuration()
    session = read_session()

    if session.strip() != "":
        print(f"Session found: {session}")
        refreshed_session = session_refresh(session)
        print("Refreshed session", refreshed_session)
        ipc_.set("current_session", refreshed_session)
    else:
        print("No session found")

def main():
    """Main application function"""
    # Initialize IPC
    global ipc_
    ipc_ = IPC.connect()

    # Set initial states
    ipc_.set("platform", PlatformDetector.get_os_name())
    ipc_.set("ollama_running", False)
    ipc_.set("internet_connected", False)
    ipc_.set("cuda_available", False)
    ipc_.set("current_session", None)
    ipc_.set("extension_version", None)
    ipc_.set("subsystem_version", None)

    print("Initializing system...")

    startup_initialization()

    # Check services
    check_ollama()
    check_internet()

    # Setup Qdrant
    qdrant_process = setup_qdrant()

    # Check CUDA (the module does the check internally)
    try:
        import BASE.utils.check_cuda
        print("CUDA check completed")
    except Exception as e:
        print(f"CUDA check failed: {e}")

    # Start servers
    server_processes = start_servers()
    
    # Keep main process alive
    try:
        print("System ready. Press Ctrl+C to exit.")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nShutting down...")
        
        # Clean up processes
        for process in server_processes:
            if process.poll() is None:
                process.terminate()
        
        if qdrant_process and qdrant_process.poll() is None:
            qdrant_process.terminate()
        
        print("Goodbye!")

# Main execution
if __name__ == "__main__":
    # CRITICAL: This must be the FIRST line after if __name__ == "__main__"
    setup_multiprocessing_support()
    
    # Now run the main application
    main()