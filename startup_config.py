from BASE.utils.path_selector import PathSelector
from logger.log import logger
from BASE.services.knowledge_bases import get_knowledge_bases_collection
import json
from pathlib import Path
import requests
from constants import auth

@logger.catch()
def extract_kb_paths_from_database() -> list[str]:
    """Extract all paths from the knowledge bases database"""
    kb_paths = []
    kb_collection = get_knowledge_bases_collection()
    for kb in kb_collection.find():
        kb_paths.append(kb["metadata"]["path"])
    return kb_paths


@logger.catch()
def perform_startup_configuration() -> bool:
    codemate_folder = PathSelector.get_base_path()
    if not codemate_folder.exists():
        logger.error(f".codemate folder does not exist at {codemate_folder}")
        return False
    
    
    
    path_config_file = codemate_folder / "file_path.json"
    if path_config_file.exists():
        logger.info(f"File path config file already exist at {path_config_file}")
        return False
    
    kb_paths = extract_kb_paths_from_database()

    if kb_paths:
        config_data = {
            "monitored_paths": kb_paths
        }
        try:
            with open(path_config_file, "w") as f:
                json.dump(config_data, f, indent=2)
        except Exception as e:
            logger.error(f"Error writing file path config file: {e}")
            return False
    return True 


@logger.catch()
def delete_kb_path(kb_path: str) -> bool:
    """Delete a path from monitored_paths in file_path.json"""
    codemate_folder = PathSelector.get_base_path()
    config_path = codemate_folder / "file_path.json"

    logger.info(f"Deleting path: {kb_path}")

    if not codemate_folder.exists():
        logger.error(f"Base folder does not exist: {codemate_folder}")
        return False

    if not config_path.exists():
        logger.error(f"Config file does not exist: {config_path}")
        return False

    try:
        with open(config_path, "r") as f:
            config = json.load(f)

        monitored_paths = config.get("monitored_paths")
        if not isinstance(monitored_paths, list):
            logger.error("Invalid structure: 'monitored_paths' should be a list.")
            return False


        for path in monitored_paths:
            if path == kb_path:
                monitored_paths.remove(path)
                break

        with open(config_path, "w") as f:
            json.dump(config, f, indent=2)

    except (json.JSONDecodeError, OSError) as e:
        logger.error(f"Failed to update file: {e}")
        return False


def add_file_path(file_path: str) -> bool:
    """Add a path to monitored_paths in file_path.json"""
    codemate_folder = PathSelector.get_base_path()
    config_path = codemate_folder / "file_path.json"
    
    if not codemate_folder.exists():
        logger.error(f"Base folder does not exist: {codemate_folder}")
        return False
    
    if not config_path.exists():
        # make file 
        with open(config_path, "w") as f:
            json.dump({"monitored_paths": []}, f, indent=2)
    
    try:
        with open(config_path, "r") as f:
            config = json.load(f)
            
        monitored_paths = config.get("monitored_paths")
        if not isinstance(monitored_paths, list):
            logger.error("Invalid structure: 'monitored_paths' should be a list.")
            return False
        
        
        for file in monitored_paths:
            if file == file_path:
                logger.warning(f"Path already exists in monitored_paths: {file_path}")
                return False
            
        
        monitored_paths.append(file_path)
        
        with open(config_path, "w") as f:
            json.dump(config, f, indent=2)
            
        logger.info(f"Added path: {file_path}")
        return True
    
    except (json.JSONDecodeError, OSError) as e:
        logger.error(f"Failed to update file: {e}")
        return False
    


def save_session(session_id: str) -> bool:
        """Save session id to session.txt, overwriting any existing session"""
        codemate_folder = PathSelector.get_base_path()
        config_path = codemate_folder / "session.txt"

        if not codemate_folder.exists():
            logger.error(f"Base folder does not exist: {codemate_folder}")
            return False

        try:
            # Open in write mode ('w') which automatically overwrites existing content
            with open(config_path, "w") as f:
                f.write(session_id)
            logger.info(f"Session {session_id} saved successfully")
        except OSError as e:
            logger.error(f"Failed to save session: {e}")
            return False
        return True

def read_session() -> str:
    """Read session id from session.txt"""
    codemate_folder = PathSelector.get_base_path()
    config_path = codemate_folder / "session.txt"
    
    if not codemate_folder.exists():
        logger.error(f"Base folder does not exist: {codemate_folder}")
        return ""
    
    if not config_path.exists():
        logger.error(f"Session file does not exist: {config_path}")
        return ""
    
    try:
        with open(config_path, "r") as f:
            session_id = f.read()
        return session_id
    except OSError as e:
        logger.error(f"Failed to read session: {e}")
        return ""
    
def delete_session() -> bool:
    """Delete session id from session.txt"""
    codemate_folder = PathSelector.get_base_path()
    config_path = codemate_folder / "session.txt"
    
    if not codemate_folder.exists():
        logger.error(f"Base folder does not exist: {codemate_folder}")
        return False
    
    if not config_path.exists():
        logger.error(f"Session file does not exist: {config_path}")
        return False
    
    try:
        config_path.unlink()
        return True
    except OSError as e:
        logger.error(f"Failed to delete session: {e}")
        return False



@logger.catch()
def session_refresh(session_id: str):
    """Refresh session id from session.txt"""
    try:
        response = requests.post(
            f"{auth}/session/refresh",
            json={},
            headers={"Content-Type": "application/json", "x-session": session_id},
        )
        if response.status_code == 200:
            result =  response.json()
            save_session(session_id=result.get('session_token'))
            return result.get('session_token')
        else:
            raise Exception(f"Error refreshing session: {response.status_code}")
    except Exception as e:
        logger.error(f"Error refreshing session: {e}")
        raise