import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

import json
from typing import Dict, Any, Optional
from datetime import datetime
from BASE.utils.path_selector import PathSelector

def migrate_knowledge_bases(old_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Migrate knowledge bases from old format to new format.
    
    Args:
        old_data: The old state.json data structure
        
    Returns:
        Dict with migrated knowledge_bases structure
    """
    
    # Extract the knowledgebase data from the old format
    old_knowledgebases = old_data.get("1.2", {}).get("knowledgebase", {}).get("knowledgebases", {})
    
    if not old_knowledgebases:
        print("No knowledgebases found in old data")
        return {"knowledge_bases": {}}
    
    new_knowledge_bases = {}
    counter = 1
    name_to_local_mapping = {}  # Track local KBs by name for cloud association
    
    # First pass: Process all knowledge bases and identify local ones
    for old_id, kb_data in old_knowledgebases.items():
        kb_name = kb_data.get("name", "")
        kb_source = kb_data.get("source", "LOCAL")
        
        # Store mapping for local KBs (without " (Cloud)" suffix)
        if kb_source == "LOCAL" and not kb_name.endswith(" (Cloud)"):
            clean_name = kb_name.strip()
            name_to_local_mapping[clean_name] = {
                "counter": counter,
                "kb_data": kb_data
            }
        
        counter += 1
    
    # Second pass: Process all knowledge bases with proper cloud_id assignment
    counter = 1
    for old_id, kb_data in old_knowledgebases.items():
        kb_name = kb_data.get("name", "")
        kb_source = kb_data.get("source", "LOCAL")
        
        # Check if this is a cloud version of an existing local KB
        if kb_name.endswith(" (Cloud)") and kb_source == "REMOTE":
            # This is a cloud version - find the corresponding local version
            base_name = kb_name.replace(" (Cloud)", "").strip()
            
            if base_name in name_to_local_mapping:
                # Use the same counter as the local version
                local_info = name_to_local_mapping[base_name]
                new_id = str(local_info["counter"])
                
                # Update the existing local KB with cloud information
                if new_id in new_knowledge_bases:
                    new_knowledge_bases[new_id]["cloud_id"] = kb_data.get("cloud_id") or kb_data.get("id")
                    print(f"Associated cloud KB '{kb_name}' with local KB '{base_name}' (ID: {new_id})")
                continue
        
        # Process regular knowledge bases (local or standalone remote)
        new_id = str(counter)
        
        # Build the new knowledge base structure
        new_kb = {
            "id": kb_data.get("id", old_id),
            "cloud_id": None,  # Will be set later if there's a corresponding cloud version
            "name": kb_name,
            "description": kb_data.get("description", ""),
            "type": kb_data.get("type", "codebase"),
            "source": kb_source,
            "scope": kb_data.get("scope", "personal"),
            "syncConfig": kb_data.get("syncConfig", {
                "enabled": False,
                "lastSynced": None
            }),
            "isAutoIndexed": kb_data.get("isAutoIndexed", False),
            "status": kb_data.get("status", "ready")
        }
        
        # Add metadata if it exists
        if "metadata" in kb_data:
            metadata = kb_data["metadata"].copy()
            
            # Add file_timestamps if files exist but timestamps don't
            if "files" in metadata and "file_timestamps" not in metadata:
                current_timestamp = int(datetime.now().timestamp() * 1000)
                metadata["file_timestamps"] = {
                    file_path: current_timestamp for file_path in metadata["files"]
                }
            
            new_kb["metadata"] = metadata
        
        
        new_knowledge_bases[new_id] = new_kb
        counter += 1
    
    return {"knowledge_bases": new_knowledge_bases}

def migrate_state_file(input_file: str, output_file: str) -> bool:
    """
    Migrate a complete state.json file from old format to new format.
    
    Args:
        input_file: Path to the old state.json file
        output_file: Path to save the migrated state.json file
        
    Returns:
        bool: True if migration successful, False otherwise
    """
    try:
        # Read the old state file
        with open(input_file, 'r', encoding='utf-8') as f:
            old_data = json.load(f)
        
        print(f"Loaded old state file: {input_file}")
        
        # Perform migration
        migrated_data = migrate_knowledge_bases(old_data)
        
        # Save the migrated data
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(migrated_data, f, indent=4, ensure_ascii=False)
        
        print(f"Migration completed successfully!")
        print(f"Migrated data saved to: {output_file}")
        
        # Print summary
        kb_count = len(migrated_data.get("knowledge_bases", {}))
        cloud_kb_count = sum(1 for kb in migrated_data.get("knowledge_bases", {}).values() 
                           if kb.get("cloud_id") is not None)
        
        print(f"Summary:")
        print(f"  - Total knowledge bases migrated: {kb_count}")
        print(f"  - Knowledge bases with cloud associations: {cloud_kb_count}")
        
        return True
        
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found")
        return False
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in input file - {e}")
        return False
    except Exception as e:
        print(f"Error during migration: {e}")
        return False

def validate_migration(migrated_file: str) -> bool:
    """
    Validate the migrated knowledge bases structure.
    
    Args:
        migrated_file: Path to the migrated state.json file
        
    Returns:
        bool: True if validation passes, False otherwise
    """
    try:
        with open(migrated_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"\nValidating migrated file: {migrated_file}")
        
        # Check if knowledge_bases exists
        if "knowledge_bases" not in data:
            print("❌ Error: 'knowledge_bases' key not found")
            return False
        
        knowledge_bases = data["knowledge_bases"]
        
        if not isinstance(knowledge_bases, dict):
            print("❌ Error: 'knowledge_bases' should be a dictionary")
            return False
        
        print(f"✅ Found {len(knowledge_bases)} knowledge bases")
        
        # Validate each knowledge base
        required_fields = ["id", "name", "type", "source", "scope", "syncConfig", "status"]
        
        for kb_id, kb_data in knowledge_bases.items():
            print(f"\n📁 Validating KB ID: {kb_id}")
            print(f"   Name: {kb_data.get('name', 'N/A')}")
            print(f"   Source: {kb_data.get('source', 'N/A')}")
            print(f"   Cloud ID: {kb_data.get('cloud_id', 'None')}")
            
            # Check required fields
            for field in required_fields:
                if field not in kb_data:
                    print(f"❌ Error: Missing required field '{field}' in KB {kb_id}")
                    return False
            
            # Validate syncConfig structure
            sync_config = kb_data.get("syncConfig", {})
            if not isinstance(sync_config, dict):
                print(f"❌ Error: syncConfig should be a dictionary in KB {kb_id}")
                return False
            
            if "enabled" not in sync_config:
                print(f"❌ Error: syncConfig missing 'enabled' field in KB {kb_id}")
                return False
        
        print("\n✅ All validations passed!")
        return True
        
    except Exception as e:
        print(f"❌ Validation error: {e}")
        return False

# Example usage and testing functions
def run_kb_migration():
    """
    Example function showing how to use the migration script.
    """
    base_path = PathSelector.get_base_path()
    print(f"base_path: ", base_path)

    old_kb_file = base_path / "state.json"  # Your old state file
    new_kb_file = base_path / "knowledge_bases_db_codemate.json"  # Output file for migrated data
    
    # Create the output file if it doesn't exist
    if not new_kb_file.exists():
        new_kb_file.touch()
    
    print("Starting Knowledge Base Migration...")
    print("=" * 50)
    
    # Perform migration
    success = migrate_state_file(old_kb_file, new_kb_file)
    
    if success:
        # Validate the migration
        validate_migration(new_kb_file)
    else:
        print("Migration failed!")

