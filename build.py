import base64
import zipfile
from pathlib import Path
import subprocess
import shutil

# === CONFIG ===
ZIP_PATH = Path("local_subsystem_bundle.zip")  # your zip file
OUTPUT_SCRIPT = Path("launch_local_subsystem.py")
FINAL_EXE_NAME = "local_subsystem_launcher.exe"
LOCAL_SUBSYSTEM = r"C:\Users\<USER>\.codemate\local_subsystem"
VENV_PYTHON = r"C:\Users\<USER>\.codemate\codemate-interpreter\Scripts\python.exe"
# ==============

def encode_zip_to_b64(zip_path):
    with open(zip_path, "rb") as f:
        encoded = base64.b64encode(f.read()).decode("utf-8")
    return encoded

def write_launcher_script(b64_data):
    launcher_code = f'''import os
import zipfile
import subprocess
import base64
from pathlib import Path
from io import BytesIO

EMBEDDED_ZIP_B64 = \"\"\"{b64_data}\"\"\"

LOCAL_SUBSYSTEM = r"{LOCAL_SUBSYSTEM}"
VENV_PYTHON = r"{VENV_PYTHON}"

def unzip_embedded_zip():
    os.makedirs(LOCAL_SUBSYSTEM, exist_ok=True)
    zip_bytes = base64.b64decode(EMBEDDED_ZIP_B64)
    with zipfile.ZipFile(BytesIO(zip_bytes)) as zipf:
        zipf.extractall(LOCAL_SUBSYSTEM)

def launch():
    unzip_embedded_zip()
    subprocess.run([VENV_PYTHON, "initiate.py"], cwd=LOCAL_SUBSYSTEM)

if __name__ == "__main__":
    launch()
'''
    OUTPUT_SCRIPT.write_text(launcher_code)
    print(f"[+] Launcher script written to: {OUTPUT_SCRIPT}")

def build_exe(script_path):
    print("[*] Building EXE using pyinstaller...")
    subprocess.run([
        "pyinstaller",
        "--onefile",
        "--name", FINAL_EXE_NAME.replace(".exe", ""),
        script_path.name
    ], check=True)
    dist_path = Path("dist") / FINAL_EXE_NAME
    if dist_path.exists():
        shutil.copy(dist_path, FINAL_EXE_NAME)
        print(f"[✅] Build successful. Final EXE: {FINAL_EXE_NAME}")
    else:
        print("[❌] Build failed. EXE not found.")

if __name__ == "__main__":
    if not ZIP_PATH.exists():
        print(f"[❌] Zip file not found: {ZIP_PATH}")
        exit(1)

    print("[*] Encoding zip to base64...")
    b64_zip = encode_zip_to_b64(ZIP_PATH)

    print("[*] Writing launcher script with embedded zip...")
    write_launcher_script(b64_zip)

    build_exe(OUTPUT_SCRIPT)
