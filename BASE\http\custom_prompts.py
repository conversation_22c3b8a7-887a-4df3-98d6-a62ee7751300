import requests
import json
import constants
from ipc import IPC


ipc_ = IPC.connect()


async def create_prompt(prompt, name):
    url = constants.cloud + "/prompts/create"
    headers = {
        "x-session": ipc_.get("session"),
        "Content-Type": "application/json",
    }

    response = requests.post(url, headers=headers, json={"prompt": prompt, "name": name})
    return response.json()


async def get_prompts():
    url = constants.cloud + "/prompts/get"
    headers = {
        "x-session": ipc_.get("session"),
        "Content-Type": "application/json",
    }

    response = requests.get(url, headers=headers)
    return response.json()


async def edit_prompt(prompt, name):
    url = constants.cloud + "/prompts/edit"
    headers = {
        "x-session": ipc_.get("session"),
        "Content-Type": "application/json",
    }

    response = requests.post(url, headers=headers, json={"name": name, "prompt": prompt})
    return response.json()


async def delete_prompt(prompt_id):
    url = constants.cloud + "/prompts/delete"
    headers = {
        "x-session": ipc_.get("session"),
        "Content-Type": "application/json",
    }

    response = requests.post(url, headers=headers, json={"id": prompt_id})
    return response.json()