from logger.log import logger
from ipc import IPC
from constants import llm_

ipc_ = IPC.connect()


@logger.catch()
def get_model_name(operation: str) -> str:
    """Get the model name for a given operation."""
    if operation == "chat":
        return "openai/bodh-x1"
    elif operation == "swagger":
        return "gpt-4.1-mini"
    elif operation == "chat_followup":
        return "gpt-4.1-mini"
    elif operation == "auto_review":
        return "gpt-4.1-mini"
    elif operation == "auto_security_scan":
        return "gpt-4.1-mini"
    elif operation == "auto_apply":
        return "gpt-4.1-mini"
    elif operation == "auto_fix_suggestions":
        return "gpt-4.1-mini"
    elif operation == "auto_document":
        return "gpt-4.1-mini"
    elif operation == "debug_code":
        return "gpt-4.1-mini"
    elif operation == "optimize_code":
        return "gpt-4.1-mini"
    elif operation == "review_code":
        return "gpt-4.1-mini"
    elif operation == "test_code":
        return "gpt-4.1-mini"
    elif operation == "swagger_generate_query":
        return "gpt-4.1-mini"
    elif operation == "swagger_generate_call":
        return "gpt-4.1-mini"
    elif operation == "swagger_structure_output":
        return "gpt-4.1-mini"
    elif operation == "swagger_generate_summary":
        return "gpt-4.1-mini"
    elif operation == "swagger_query_api":
        return "gpt-4.1-mini"
    elif operation == "swagger_generate_code_imports":
        return "gpt-4.1-mini"
    else:
        return "gpt-4.1-mini"


@logger.catch()
def llm_config(operation: str):
    session = ipc_.get("current_session", None)
    if not session:
        logger.error("No session provided for llm config")
        raise ValueError("No session provided for llm config")
    
    model_name = get_model_name(operation)
    return {
        "base_url": llm_,
        "api_key": session,
        "model": model_name,
    }