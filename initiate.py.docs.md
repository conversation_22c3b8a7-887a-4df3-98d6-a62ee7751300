# Optimized Python Code Documentation

This document describes the optimized version of the provided Python script, which manages system initialization, service checks, and server startups with improved structure, error handling, and efficiency.

---

## Overview

The script performs the following key tasks:

- Initializes inter-process communication (IPC) states.
- Checks if specific services (Ollama, internet connectivity) are running.
- Downloads and starts the Qdrant vector database server if needed.
- Checks CUDA availability.
- Starts HTTP and WebSocket servers.
- Maintains the main process alive until interrupted, with graceful shutdown.

---

## Optimization Summary

- **Reduced redundant imports** and grouped logically.
- **Improved exception handling** with specific exceptions where possible.
- **Refactored repeated code** into reusable functions.
- **Added type hints** for better readability and maintainability.
- **Used context managers and clearer resource management.**
- **Enhanced logging with consistent formatting.**
- **Simplified boolean checks and string handling.**
- **Ensured subprocesses are terminated gracefully on exit.**

---

## Code Explanation

```python
import socket
import subprocess
import sys
import time
from typing import Optional, List

import requests

from ipc import IPC
from BASE.utils.platform_detector import PlatformDetector
from BASE.vdb.qdrant import get_qdrant_client, start_qdrant_server
from BASE.vdb.download import main as download_qdrant
from startup_config import perform_startup_configuration, read_session, session_refresh

# Initialize IPC and set initial states
ipc_ = IPC.connect()
ipc_.set("platform", PlatformDetector.get_os_name())
ipc_.set("ollama_running", False)
ipc_.set("internet_connected", False)
ipc_.set("cuda_available", False)
ipc_.set("current_session", None)
ipc_.set("extension_version", None)
ipc_.set("subsystem_version", None)
ipc_.set("qdrant_downloaded", False)
ipc_.set("qdrant_ready", False)


def is_port_open(host: str = "localhost", port: int = 11434, timeout: float = 3.0) -> bool:
    """Check if a TCP port is open on the specified host."""
    try:
        with socket.create_connection((host, port), timeout=timeout):
            return True
    except (socket.timeout, ConnectionRefusedError, OSError):
        return False


def check_ollama() -> None:
    """Update IPC state based on whether Ollama service is running."""
    running = is_port_open(port=11434)
    ipc_.set("ollama_running", running)
    print(f"Ollama: {'Running' if running else 'Not running'}")


def check_internet() -> None:
    """Update IPC state based on internet connectivity."""
    try:
        response = requests.get("https://www.google.com", timeout=5)
        connected = response.status_code == 200
    except requests.RequestException:
        connected = False
    ipc_.set("internet_connected", connected)
    print(f"Internet: {'Connected' if connected else 'Disconnected'}")


def setup_qdrant() -> Optional[subprocess.Popen]:
    """Download, start, and verify Qdrant server readiness."""
    print("Checking Qdrant download...")
    downloaded = download_qdrant()
    ipc_.set("qdrant_downloaded", downloaded)

    if not downloaded:
        print("Qdrant download failed")
        ipc_.set("qdrant_ready", False)
        return None

    if is_port_open(port=45215):
        print("Qdrant already running")
        ipc_.set("qdrant_ready", True)
        return None

    try:
        print("Starting Qdrant server...")
        qdrant_process = start_qdrant_server()

        for _ in range(30):  # Wait up to 30 seconds for Qdrant to be ready
            if is_port_open(port=45215):
                ipc_.set("qdrant_ready", True)
                print("Qdrant server ready")
                return qdrant_process
            time.sleep(1)

        print("Qdrant server failed to start within timeout")
        ipc_.set("qdrant_ready", False)
        return qdrant_process

    except Exception as e:
        print(f"Qdrant setup failed: {e}")
        ipc_.set("qdrant_ready", False)
        return None


def start_servers() -> List[subprocess.Popen]:
    """Start HTTP and WebSocket servers as subprocesses."""
    servers = ['http_server.py', 'websocket_server.py']
    processes = []
    for server in servers:
        try:
            process = subprocess.Popen([sys.executable, server])
            processes.append(process)
            print(f"Started {server} (PID: {process.pid})")
        except Exception as e:
            print(f"Failed to start {server}: {e}")
    return processes


def startup_initialization() -> None:
    """Perform startup configuration and session refresh."""
    perform_startup_configuration()
    session = read_session().strip()

    if session:
        print(f"Session found: {session}")
        refreshed_session = session_refresh(session)
        ipc_.set("current_session", refreshed_session)
    else:
        print("No session found")


def check_cuda_availability() -> None:
    """Check CUDA availability and update IPC state."""
    try:
        import BASE.utils.check_cuda  # Module performs internal checks on import
        ipc_.set("cuda_available", True)
        print("CUDA check completed")
    except ImportError:
        ipc_.set("cuda_available", False)
        print("CUDA check failed: Module not found")
    except Exception as e:
        ipc_.set("cuda_available", False)
        print(f"CUDA check failed: {e}")


def main() -> None:
    """Main entry point for system initialization and service management."""
    print("Initializing system...")

    startup_initialization()
    check_ollama()
    check_internet()
    qdrant_process = setup_qdrant()
    check_cuda_availability()
    server_processes = start_servers()

    try:
        print("System ready. Press Ctrl+C to exit.")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nShutting down...")

        # Terminate server subprocesses
        for process in server_processes:
            if process.poll() is None:
                process.terminate()
                process.wait(timeout=5)

        # Terminate Qdrant process if started
        if qdrant_process and qdrant_process.poll() is None:
            qdrant_process.terminate()
            qdrant_process.wait(timeout=5)

        print("Goodbye!")


if __name__ == "__main__":
    main()
```

---

## Key Improvements

- **`is_port_open`** uses `socket.create_connection` for cleaner connection attempts.
- Added **`check_cuda_availability`** function to encapsulate CUDA check and IPC update.
- Added **`main()`** function to encapsulate script logic improving readability and reusability.
- Graceful termination of subprocesses with `wait(timeout=5)` to ensure proper cleanup.
- IPC keys for Qdrant download and readiness states are initialized and updated consistently.
- Exception handling is more specific, avoiding bare excepts.
- Removed redundant imports and comments for clarity.
- Added type hints for function signatures.

---

This optimized code is more maintainable, robust, and clear, while preserving the original functionality.